import { getBrandLogo } from '../services/utils.js';

export const marketplace = () => ({
  // State
  selectedBrand: 'all',
  selectedModel: 'all',
  sortBy: 'recent',
  showDetailModal: false,
  selectedTruck: null,
  showBrandGrid: false,
  showModelGrid: false,
  showBrandModal: false,
  showModelModal: false,
  currentImageIndex: 0,

  // Pagination state
  currentPage: 1,
  itemsPerPage: 12,
  itemsPerPageOptions: [12, 20, 30],
  
  // Filters  
  brands: [
    'all',
    'BMW',
    'Calmar',
    'Caterpillar',
    'Dacia',
    'DAF',
    'Foton',
    'Iveco',
    'Jaguar',
    'Lohr',
    'MAN',
    'Mercedes-Benz',
    'Peugeot',
    'Renault',
    'Scania',
    'Volkswagen',
    'Volvo',
    'Zettelmeyer'
  ],
  
  // Computed properties
  get availableModels() {
    const trucks = this.$store.app.marketplaceTrucks || [];
    const models = new Set();

    trucks.forEach(truck => {
      if (this.selectedBrand === 'all' || truck.brand.toLowerCase() === this.selectedBrand.toLowerCase()) {
        models.add(truck.model);
      }
    });

    return ['all', ...Array.from(models).sort()];
  },

  get filteredTrucks() {
    let trucks = this.$store.app.marketplaceTrucks || [];

    // Filter by brand
    if (this.selectedBrand !== 'all') {
      trucks = trucks.filter(truck => truck.brand.toLowerCase() === this.selectedBrand.toLowerCase());
    }

    // Filter by model
    if (this.selectedModel !== 'all') {
      trucks = trucks.filter(truck => truck.model.toLowerCase() === this.selectedModel.toLowerCase());
    }

    // Sort
    trucks = [...trucks].sort((a, b) => {
      switch (this.sortBy) {
        case 'price_low':
          return a.price - b.price;
        case 'price_high':
          return b.price - a.price;
        case 'year_new':
          return b.year - a.year;
        case 'year_old':
          return a.year - b.year;
        case 'km_low':
          return a.kilometers - b.kilometers;
        case 'km_high':
          return b.kilometers - a.kilometers;
        case 'recent':
        default:
          return new Date(b.dateAdded) - new Date(a.dateAdded);
      }
    });

    return trucks;
  },

  // Pagination computed properties
  get totalTrucks() {
    return this.filteredTrucks.length;
  },

  get totalPages() {
    return Math.ceil(this.totalTrucks / this.itemsPerPage);
  },

  get paginatedTrucks() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    return this.filteredTrucks.slice(startIndex, endIndex);
  },

  get paginationInfo() {
    const startItem = this.totalTrucks === 0 ? 0 : (this.currentPage - 1) * this.itemsPerPage + 1;
    const endItem = Math.min(this.currentPage * this.itemsPerPage, this.totalTrucks);
    return {
      startItem,
      endItem,
      totalItems: this.totalTrucks
    };
  },

  get visiblePageNumbers() {
    const maxVisible = 5; // Show max 5 page numbers for tablet
    const pages = [];
    const totalPages = this.totalPages;

    if (totalPages <= maxVisible) {
      // Show all pages if total is less than max
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      let start = Math.max(1, this.currentPage - 2);
      let end = Math.min(totalPages, this.currentPage + 2);

      // Adjust if we're near the beginning or end
      if (this.currentPage <= 3) {
        end = Math.min(totalPages, 5);
      } else if (this.currentPage >= totalPages - 2) {
        start = Math.max(1, totalPages - 4);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
    }

    return pages;
  },
  // Computed properties (add to existing computed section)
get hasActiveFilters() {
  return this.selectedBrand !== 'all' || this.selectedModel !== 'all';
},

// Methods (add to existing methods section)
clearBrandFilter() {
  this.selectedBrand = 'all';
  this.selectedModel = 'all'; // Reset model when clearing brand
},

clearModelFilter() {
  this.selectedModel = 'all';
},

clearAllFilters() {
  this.selectedBrand = 'all';
  this.selectedModel = 'all';
  this.sortBy = 'recent';
},


  // Methods
  init() {
    // Initialize marketplace data if not already loaded
    if (!this.$store.app.marketplaceTrucks) {
      this.$store.app.initializeMarketplaceData();
    }

    // Initialize lazy loading for images
    this.initLazyLoading();
  },

  initLazyLoading() {
    // Create intersection observer for lazy loading images
    this.imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.dataset.src;
          if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            this.imageObserver.unobserve(img);
          }
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.1
    });
  },

  observeImage(img) {
    if (this.imageObserver && img) {
      this.imageObserver.observe(img);
    }
  },

  selectBrand(brand) {
    this.selectedBrand = brand;
    // Reset model when brand changes
    if (brand !== 'all') {
      this.selectedModel = 'all';
    }
    this.showBrandGrid = false;
    this.resetPagination(); // Reset to first page when brand changes
  },

  selectModel(model) {
    this.selectedModel = model;
    this.showModelGrid = false;
    this.resetPagination(); // Reset to first page when model changes
  },

  setSortBy(sortBy) {
    this.sortBy = sortBy;
  },

  toggleBrandGrid() {
    this.showBrandGrid = !this.showBrandGrid;
    this.showModelGrid = false;
  },

  toggleModelGrid() {
    this.showModelGrid = !this.showModelGrid;
    this.showBrandGrid = false;
  },

  closeBrandGrid() {
    this.showBrandGrid = false;
  },

  closeModelGrid() {
    this.showModelGrid = false;
  },

  getBrandLogo(brand) {
    return getBrandLogo(brand);
  },

  showTruckDetails(truck) {
    this.selectedTruck = truck;
    this.currentImageIndex = 0; // Reset image index
    this.showDetailModal = true;
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  },

  closeDetailModal() {
    this.showDetailModal = false;
    this.selectedTruck = null;
    this.currentImageIndex = 0;
    // Restore body scroll
    document.body.style.overflow = '';
  },

  // Image gallery methods
  nextImage() {
    if (this.selectedTruck && this.selectedTruck.images.length > 1) {
      this.currentImageIndex = (this.currentImageIndex + 1) % this.selectedTruck.images.length;
    }
  },

  prevImage() {
    if (this.selectedTruck && this.selectedTruck.images.length > 1) {
      this.currentImageIndex = this.currentImageIndex === 0 
        ? this.selectedTruck.images.length - 1 
        : this.currentImageIndex - 1;
    }
  },

  selectImage(index) {
    this.currentImageIndex = index;
  },

  // Touch/swipe handling for images
  handleImageSwipe(direction) {
    if (direction === 'left') {
      this.nextImage();
    } else if (direction === 'right') {
      this.prevImage();
    }
  },

  formatPrice(price) {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  },

  formatKilometers(km) {
    return new Intl.NumberFormat('fr-FR').format(km) + ' km';
  },


  contactSeller() {
    // This would typically open a contact form or redirect to contact page
    alert(this.$store.app.t('contact_seller_message'));
  },

  // Pagination methods
  goToPage(page) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
    }
  },

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
    }
  },

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  },

  changeItemsPerPage(newItemsPerPage) {
    this.itemsPerPage = newItemsPerPage;
    this.resetPagination();
  },

  resetPagination() {
    this.currentPage = 1;
  }
});
